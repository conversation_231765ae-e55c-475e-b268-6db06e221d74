#include "sys_selftest.h"
#include "sys_rtc.h"
#include "gd25qxx.h"

static sys_selftest_result_t selftest_result; // 自检结果存储

void sys_selftest_init(void) {
    memset(&selftest_result, 0, sizeof(selftest_result)); // 清零结果结构体
}

sys_selftest_result_t sys_selftest_run(void) {
    // Flash检测
    selftest_result.flash_id = spi_flash_read_id(); // 读取Flash ID
    selftest_result.flash_ok = (selftest_result.flash_id != 0 && selftest_result.flash_id != 0xFFFFFF) ? 1 : 0;
    
    // TF卡检测 - 通过LittleFS挂载状态判断
    extern lfs_t lfs;
    selftest_result.tf_card_ok = (lfs.cfg != NULL) ? 1 : 0; // 检查文件系统是否已挂载
    
    // RTC检测
    selftest_result.rtc_ok = sys_rtc_check_status(); // 检查RTC状态
    
    // 获取RTC时间
    sys_rtc_time_t rtc_time;
    if (sys_rtc_get_time(&rtc_time)) {
        sys_rtc_format_time_string(&rtc_time, selftest_result.rtc_time); // 格式化时间字符串
    } else {
        strcpy(selftest_result.rtc_time, "RTC Error"); // RTC错误
    }
    
    return selftest_result; // 返回自检结果
}

void sys_selftest_print_result(sys_selftest_result_t *result) {
    my_printf(&huart1, "--------system selftest--------\r\n");
    
    // Flash检测结果
    if (result->flash_ok) {
        my_printf(&huart1, "flash..........ok\r\n");
    } else {
        my_printf(&huart1, "flash..........error\r\n");
    }
    
    // TF卡检测结果
    if (result->tf_card_ok) {
        my_printf(&huart1, "TF card........ok\r\n");
    } else {
        my_printf(&huart1, "TF card........error\r\n");
    }
    
    // Flash ID显示
    my_printf(&huart1, "flash ID: 0x%06X\r\n", (unsigned int)result->flash_id);
    
    // TF卡信息显示
    if (!result->tf_card_ok) {
        my_printf(&huart1, "can not find TF card\r\n");
    } else {
        extern lfs_t lfs;
        lfs_ssize_t total_size = lfs_fs_size(&lfs); // 获取文件系统大小
        if (total_size > 0) {
            my_printf(&huart1, "TF card memory: %d KB\r\n", (int)(total_size * lfs.cfg->block_size / 1024));
        }
    }
    
    // RTC时间显示
    my_printf(&huart1, "RTC: %s\r\n", result->rtc_time);
    
    my_printf(&huart1, "--------system selftest--------\r\n");
}
