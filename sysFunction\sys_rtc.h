#ifndef __SYS_RTC_H__
#define __SYS_RTC_H__

#include "mydefine.h"

// RTC时间结构体
typedef struct {
    uint16_t year;   // 年
    uint8_t month;   // 月
    uint8_t day;     // 日
    uint8_t hour;    // 时
    uint8_t minute;  // 分
    uint8_t second;  // 秒
} sys_rtc_time_t;

// 函数声明
void sys_rtc_init(void);                                        // RTC初始化
uint8_t sys_rtc_set_time(sys_rtc_time_t *time);                // 设置RTC时间
uint8_t sys_rtc_get_time(sys_rtc_time_t *time);                // 获取RTC时间
uint8_t sys_rtc_parse_time_string(const char *time_str, sys_rtc_time_t *time); // 解析时间字符串
void sys_rtc_format_time_string(sys_rtc_time_t *time, char *str); // 格式化时间字符串
uint8_t sys_rtc_check_status(void);                            // 检查RTC状态

#endif /* __SYS_RTC_H__ */
