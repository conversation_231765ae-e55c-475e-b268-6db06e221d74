# sysFunction 系统功能模块

## 概述
sysFunction模块实现了系统自检、RTC时间管理等核心系统功能，所有功能都集成到shell命令系统中。

## 功能特性

### 1. 系统自检功能
- **命令**: `test`
- **功能**: 执行完整的系统自检，包括Flash、TF卡、RTC检测
- **输出示例**:
```
--------system selftest--------
flash..........ok
TF card........ok
flash ID: 0xC84016
TF card memory: 1024 KB
RTC: 2025-01-01 12:00:30
--------system selftest--------
```

### 2. RTC时间管理
- **命令**: `rtc`
- **子命令**:
  - `rtc` - 显示当前时间
  - `rtc config "2025-01-01 12:00:30"` - 设置时间
  - `rtc now` - 查询当前时间

- **时间格式**: YYYY-MM-DD HH:MM:SS
- **实现方式**: 由于项目中没有rtc.h文件，使用系统tick模拟RTC功能
- **输出示例**:
```
> rtc config "2025-01-01 12:00:30"
RTC Config success
Time: 2025-01-01 12:00:30

> rtc now
Current Time: 2025-01-01 12:00:50
```

## 文件结构
```
sysFunction/
├── sys_selftest.h      # 系统自检头文件
├── sys_selftest.c      # 系统自检实现
├── sys_rtc.h          # RTC管理头文件
└── sys_rtc.c          # RTC管理实现
```

## 集成说明

### 1. 头文件包含
在 `mydefine.h` 中已添加:
```c
#include "sysFunction/sys_selftest.h"
#include "sysFunction/sys_rtc.h"
```

### 2. 调度器集成
在 `scheduler_init()` 中已添加初始化:
```c
sys_selftest_init(); // 系统自检初始化
sys_rtc_init();      // RTC初始化
```

### 3. Shell命令集成
已在shell_app.c中添加新命令:
- `test` - 系统自检命令
- `rtc` - RTC时间管理命令

## 使用方法

### 系统自检
```bash
> test
```

### RTC时间设置
```bash
> rtc config "2025-01-01 12:00:30"
```

### RTC时间查询
```bash
> rtc now
```

## 技术特点
- 代码控制在最少行数
- 右侧#注释风格
- 统一配置文件管理
- 中文友好设计
- 性能优化考虑
- 完全集成到现有架构

## 错误处理
- Flash检测失败时显示error状态
- TF卡未挂载时显示相应错误信息
- RTC故障时显示"RTC Error"
- 时间格式错误时提示正确格式

## 扩展性
模块设计具有良好的扩展性，可以轻松添加新的系统检测项目和时间管理功能。

## 注意事项
- **RTC功能**: 当前使用系统tick模拟RTC功能，如果项目中有真实的RTC硬件，请：
  1. 添加相应的rtc.h头文件
  2. 在sys_rtc.c中替换模拟实现为真实RTC API调用
  3. 在mydefine.h中添加#include "rtc.h"
- **Flash检测**: 依赖gd25qxx.h中的spi_flash_read_id()函数
- **TF卡检测**: 依赖LittleFS文件系统的挂载状态
