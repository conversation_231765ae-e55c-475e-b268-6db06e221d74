#include "sys_rtc.h"
#include "rtc.h"

extern RTC_HandleTypeDef hrtc; // RTC句柄

void sys_rtc_init(void) {
    // RTC初始化已在main.c中完成，这里可以添加额外配置
}

uint8_t sys_rtc_set_time(sys_rtc_time_t *time) {
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};
    
    // 设置时间
    sTime.Hours = time->hour;
    sTime.Minutes = time->minute;
    sTime.Seconds = time->second;
    sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    sTime.StoreOperation = RTC_STOREOPERATION_RESET;
    
    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // 设置失败
    }
    
    // 设置日期
    sDate.WeekDay = RTC_WEEKDAY_MONDAY; // 默认周一
    sDate.Month = time->month;
    sDate.Date = time->day;
    sDate.Year = time->year - 2000; // RTC年份从2000年开始
    
    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // 设置失败
    }
    
    return 1; // 设置成功
}

uint8_t sys_rtc_get_time(sys_rtc_time_t *time) {
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};
    
    // 获取时间
    if (HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // 获取失败
    }
    
    // 获取日期
    if (HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // 获取失败
    }
    
    // 填充结构体
    time->hour = sTime.Hours;
    time->minute = sTime.Minutes;
    time->second = sTime.Seconds;
    time->year = sDate.Year + 2000; // 转换为实际年份
    time->month = sDate.Month;
    time->day = sDate.Date;
    
    return 1; // 获取成功
}

uint8_t sys_rtc_parse_time_string(const char *time_str, sys_rtc_time_t *time) {
    // 解析格式: "2025-01-01 12:00:30" 或 "2025-01-01 01:30:10"
    int year, month, day, hour, minute, second;
    
    if (sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) == 6) {
        // 验证时间有效性
        if (year >= 2000 && year <= 2099 && month >= 1 && month <= 12 && 
            day >= 1 && day <= 31 && hour >= 0 && hour <= 23 && 
            minute >= 0 && minute <= 59 && second >= 0 && second <= 59) {
            
            time->year = year;
            time->month = month;
            time->day = day;
            time->hour = hour;
            time->minute = minute;
            time->second = second;
            return 1; // 解析成功
        }
    }
    
    return 0; // 解析失败
}

void sys_rtc_format_time_string(sys_rtc_time_t *time, char *str) {
    sprintf(str, "%04d-%02d-%02d %02d:%02d:%02d", 
            time->year, time->month, time->day, 
            time->hour, time->minute, time->second); // 格式化时间字符串
}

uint8_t sys_rtc_check_status(void) {
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};
    
    // 尝试读取RTC时间来检查状态
    if (HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // RTC故障
    }
    
    if (HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return 0; // RTC故障
    }
    
    return 1; // RTC正常
}
